#!/usr/bin/env python3
"""
实时弹幕聚合器启动脚本
"""

import logging
import uvicorn
from api import app

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def main():
    """主函数"""
    logger.info("启动实时弹幕聚合器...")
    
    # 启动FastAPI应用
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        log_level="info"
    )


if __name__ == "__main__":
    main()
