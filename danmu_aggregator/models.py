from pydantic import BaseModel, Field
from typing import List, Optional, Any, Dict


class DandanResponseBase(BaseModel):
    """模仿 dandanplay API v2 的基础响应模型"""
    success: bool = True
    errorCode: int = 0
    errorMessage: str = Field("", description="错误信息")


class DandanEpisodeInfo(BaseModel):
    """dandanplay /search/episodes 接口中的分集信息模型"""
    episodeId: int
    episodeTitle: str


class DandanAnimeInfo(BaseModel):
    """dandanplay /search/episodes 接口中的番剧信息模型"""
    animeId: int
    animeTitle: str
    imageUrl: str = ""
    searchKeyword: str = ""
    type: str
    typeDescription: str
    isOnAir: bool = False
    airDay: int = 0
    isFavorited: bool = False
    rating: float = 0.0
    episodes: List[DandanEpisodeInfo]


class DandanSearchEpisodesResponse(DandanResponseBase):
    """dandanplay /search/episodes 接口的响应模型"""
    animes: List[DandanAnimeInfo] = []


class Comment(BaseModel):
    """弹幕评论模型"""
    cid: int
    p: str  # 弹幕参数，格式为 "时间,模式,字号,颜色,时间戳,池,用户ID,弹幕ID"
    m: str  # 弹幕内容


class CommentResponse(BaseModel):
    """弹幕响应模型"""
    count: int
    comments: List[Comment]


class SearchResult(BaseModel):
    """搜索结果模型"""
    title: str
    url: str
    year: Optional[int] = None
    episode_count: Optional[int] = None
    image_url: Optional[str] = None
    provider: str
    media_id: str


class EpisodeInfo(BaseModel):
    """分集信息模型"""
    episode_id: str
    title: str
    url: str
    episode_index: int


class EpisodeMapping(BaseModel):
    """分集ID到播放地址的映射"""
    episode_id: int
    provider: str
    media_id: str
    episode_url: str
    anime_title: str
    episode_title: str
