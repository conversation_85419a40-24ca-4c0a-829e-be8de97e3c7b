import re
import json
import logging
from typing import List, Optional, Dict, Any, Callable
from .base import BaseScraper, ProviderSearchInfo, ProviderEpisodeInfo


class IqiyiScraper(BaseScraper):
    """爱奇艺爬虫"""
    
    @property
    def provider_name(self) -> str:
        return "iqiyi"
    
    def __init__(self):
        super().__init__()
        # 过滤非正片内容的正则表达式
        self._EPISODE_BLACKLIST_PATTERN = re.compile(
            r"预告|彩蛋|专访|直拍|直播回顾|加更|走心|解忧|纯享|节点|解读|揭秘|赏析|速看|资讯|访谈|番外|短片|纪录片|"
            r"花絮|看点|预告片|精彩|NG|特辑|菜单|片花|首映礼|宣传片|未删减|剪辑版|MV|主题曲|片尾曲|OST|纯享版|会员版|独家版|未播|抢先看|精选合集",
            re.IGNORECASE
        )

    async def search(self, keyword: str, episode_info: Optional[Dict[str, Any]] = None) -> List[ProviderSearchInfo]:
        """搜索爱奇艺"""
        self.logger.info(f"iQiyi: 正在搜索 '{keyword}'...")
        
        try:
            # 爱奇艺搜索API
            search_url = "https://search.video.iqiyi.com/o"
            params = {
                "key": keyword,
                "if": "mobile",
                "pageNum": 1,
                "pageSize": 20,
                "pos": 1
            }
            
            response = await self._request_with_rate_limit("GET", search_url, params=params)
            response.raise_for_status()
            data = response.json()
            
            results = []
            if data.get("code") == "A00000" and data.get("data", {}).get("docinfos"):
                for item in data["data"]["docinfos"]:
                    try:
                        album_info = item.get("albumDocInfo", {})
                        if not album_info:
                            continue
                        
                        # 提取媒体ID
                        album_id = album_info.get("albumId")
                        if not album_id:
                            continue
                        
                        # 判断类型
                        video_doc_type = album_info.get("videoDocType", 0)
                        media_type = "movie" if video_doc_type == 1 else "tv_series"
                        
                        # 提取年份
                        year = None
                        release_date = album_info.get("releaseDate", "")
                        if release_date and len(release_date) >= 4:
                            try:
                                year = int(release_date[:4])
                            except ValueError:
                                pass
                        
                        result = ProviderSearchInfo(
                            provider=self.provider_name,
                            mediaId=str(album_id),
                            title=album_info.get("albumTitle", ""),
                            type=media_type,
                            season=1,
                            year=year,
                            episode_count=album_info.get("itemTotalNumber", 0),
                            imageUrl=album_info.get("albumImg", ""),
                            url=album_info.get("albumLink", "")
                        )
                        results.append(result)
                        
                    except Exception as e:
                        self.logger.warning(f"解析爱奇艺搜索结果时出错: {e}")
                        continue
            
            self.logger.info(f"iQiyi: 搜索完成，找到 {len(results)} 个结果")
            return results
            
        except Exception as e:
            self.logger.error(f"爱奇艺搜索出错: {e}")
            return []

    async def get_episodes(self, media_id: str, target_episode_index: Optional[int] = None, 
                          db_media_type: Optional[str] = None) -> List[ProviderEpisodeInfo]:
        """获取爱奇艺分集列表"""
        self.logger.info(f"iQiyi: 正在获取 {media_id} 的分集列表...")
        
        try:
            # 爱奇艺分集API
            episodes_url = f"https://pcw-api.iqiyi.com/albums/album/avlistinfo"
            params = {
                "aid": media_id,
                "size": 50,
                "page": 1
            }
            
            response = await self._request_with_rate_limit("GET", episodes_url, params=params)
            response.raise_for_status()
            data = response.json()
            
            episodes = []
            if data.get("code") == "A00000" and data.get("data", {}).get("epsodelist"):
                for i, ep in enumerate(data["data"]["epsodelist"], 1):
                    try:
                        title = ep.get("name", f"第{i}集")
                        
                        # 过滤非正片内容
                        if self._EPISODE_BLACKLIST_PATTERN.search(title):
                            self.logger.debug(f"iQiyi: 过滤掉非正片内容: '{title}'")
                            continue
                        
                        episode_info = ProviderEpisodeInfo(
                            episode_id=ep.get("tvId", ""),
                            title=title,
                            episodeIndex=i,
                            url=ep.get("playUrl", "")
                        )
                        episodes.append(episode_info)
                        
                        if target_episode_index and i >= target_episode_index:
                            break
                            
                    except Exception as e:
                        self.logger.warning(f"解析爱奇艺分集信息时出错: {e}")
                        continue
            
            self.logger.info(f"iQiyi: 获取到 {len(episodes)} 个分集")
            return episodes
            
        except Exception as e:
            self.logger.error(f"获取爱奇艺分集列表出错: {e}")
            return []

    async def get_comments(self, episode_id: str, progress_callback: Optional[Callable] = None) -> List[Dict[str, Any]]:
        """获取爱奇艺弹幕"""
        self.logger.info(f"iQiyi: 正在获取弹幕 tvId={episode_id}...")
        
        try:
            # 爱奇艺弹幕API
            danmaku_url = "https://cmts.iqiyi.com/bullet"
            params = {
                "tvId": episode_id,
                "size": 300,
                "businessType": 1,
                "targetId": episode_id,
                "targetType": 1
            }
            
            response = await self._request_with_rate_limit("GET", danmaku_url, params=params)
            response.raise_for_status()
            data = response.json()
            
            comments = []
            if data.get("code") == "A00000" and data.get("data", {}).get("bullets"):
                for i, comment in enumerate(data["data"]["bullets"]):
                    try:
                        # 爱奇艺弹幕格式转换
                        show_time = float(comment.get("showTime", 0)) / 1000.0  # 毫秒转秒
                        content = comment.get("content", "")
                        
                        formatted_comment = {
                            'cid': i + 1,
                            'p': f"{show_time:.3f},1,25,16777215,{int(show_time)},0,0,{i+1}",
                            'm': content,
                            't': round(show_time, 2)
                        }
                        comments.append(formatted_comment)
                    except Exception as e:
                        self.logger.warning(f"解析爱奇艺弹幕时出错: {e}")
                        continue
            
            self.logger.info(f"iQiyi: 获取到 {len(comments)} 条弹幕")
            return comments
            
        except Exception as e:
            self.logger.error(f"获取爱奇艺弹幕出错: {e}")
            return []
