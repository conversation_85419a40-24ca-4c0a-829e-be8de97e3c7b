import asyncio
import httpx
import re
import logging
import html
import json
from typing import List, Dict, Any, Optional, Union, Callable
from pydantic import BaseModel, Field, ValidationError
from collections import defaultdict
from datetime import datetime
from .base import BaseScraper, ProviderSearchInfo, ProviderEpisodeInfo

# --- Pydantic 模型，用于解析腾讯API的响应 ---

class TencentCommentContentStyle(BaseModel):
    color: Optional[str] = None
    position: Optional[int] = None

class TencentEpisode(BaseModel):
    vid: str = Field(..., description="分集视频ID")
    title: str = Field(..., description="分集标题")
    is_trailer: str = Field("0", alias="is_trailer")
    union_title: Optional[str] = None

class TencentComment(BaseModel):
    id: str = Field(..., description="弹幕ID")
    time_offset: str = Field(..., description="弹幕时间偏移(毫秒)")
    content: str = Field(..., description="弹幕内容")
    content_style: Union[TencentCommentContentStyle, str, None] = Field(None)

# --- 用于搜索API的新模型 ---
class TencentSubjectDoc(BaseModel):
    video_num: int = Field(0, alias="videoNum")

class TencentSearchVideoInfo(BaseModel):
    title: str
    year: Optional[int] = None
    type_name: str = Field(alias="typeName")
    img_url: Optional[str] = Field(None, alias="imgUrl")
    subject_doc: Optional[TencentSubjectDoc] = Field(None, alias="subjectDoc")

class TencentSearchDoc(BaseModel):
    id: str  # 这是 cid

class TencentSearchItem(BaseModel):
    video_info: Optional[TencentSearchVideoInfo] = Field(None, alias="videoInfo")
    doc: TencentSearchDoc

class TencentSearchItemList(BaseModel):
    item_list: List[TencentSearchItem] = Field(alias="itemList")

class TencentSearchData(BaseModel):
    normal_list: Optional[TencentSearchItemList] = Field(None, alias="normalList")

class TencentSearchResult(BaseModel):
    data: Optional[TencentSearchData] = None

# --- 用于搜索API的请求模型 ---
class TencentSearchRequest(BaseModel):
    query: str
    version: str = ""
    filter_value: str = Field("firstTabid=150", alias="filterValue")
    retry: int = 0
    pagenum: int = 0
    pagesize: int = 20
    query_from: int = Field(4, alias="queryFrom")
    is_need_qc: bool = Field(True, alias="isneedQc")
    ad_request_info: str = Field("", alias="adRequestInfo")
    sdk_request_info: str = Field("", alias="sdkRequestInfo")
    scene_id: int = Field(21, alias="sceneId")
    platform: str = "23"


class TencentScraper(BaseScraper):
    """腾讯视频爬虫"""

    @property
    def provider_name(self) -> str:
        return "tencent"

    def __init__(self):
        super().__init__()
        # 过滤非正片内容的正则表达式
        self._EPISODE_BLACKLIST_PATTERN = re.compile(
            r"预告|彩蛋|专访|直拍|直播回顾|加更|走心|解忧|纯享|节点|解读|揭秘|赏析|速看|资讯|访谈|番外|短片|纪录片|"
            r"花絮|看点|预告片|精彩|NG|特辑|菜单|片花|首映礼|宣传片|未删减|剪辑版|MV|主题曲|片尾曲|OST|纯享版|会员版|独家版|未播|抢先看|精选合集",
            re.IGNORECASE
        )
        # 用于从标题中提取集数的正则表达式
        self._EPISODE_INDEX_PATTERN = re.compile(r"^(?:第)?(\d+)(?:集|话)?$")

    def _get_season_from_title(self, title: str) -> int:
        """从标题中提取季数"""
        season_patterns = [
            (re.compile(r'第([一二三四五六七八九十]+)季'), lambda m: self._chinese_to_number(m.group(1))),
            (re.compile(r'Season\s*(\d+)', re.IGNORECASE), lambda m: int(m.group(1))),
            (re.compile(r'S(\d+)', re.IGNORECASE), lambda m: int(m.group(1))),
            (re.compile(r'(\d+)$'), lambda m: int(m.group(1)))
        ]

        for pattern, handler in season_patterns:
            match = pattern.search(title)
            if match:
                try:
                    season = handler(match)
                    if season and 1 <= season <= 20:
                        return season
                except (ValueError, KeyError, IndexError):
                    continue
        return 1

    def _chinese_to_number(self, chinese_num: str) -> int:
        """将中文数字转换为阿拉伯数字"""
        chinese_map = {
            '一': 1, '二': 2, '三': 3, '四': 4, '五': 5,
            '六': 6, '七': 7, '八': 8, '九': 9, '十': 10
        }
        return chinese_map.get(chinese_num, 1)

    async def search(self, keyword: str, episode_info: Optional[Dict[str, Any]] = None) -> List[ProviderSearchInfo]:
        """通过腾讯搜索API查找番剧"""
        self.logger.info(f"Tencent: 正在搜索 '{keyword}'...")

        url = "https://pbaccess.video.qq.com/trpc.videosearch.mobile_search.HttpMobileRecall/MbSearchHttp"
        request_model = TencentSearchRequest(query=keyword)
        payload = request_model.model_dump(by_alias=True)
        results = []

        try:
            response = await self._request_with_rate_limit("POST", url, json=payload)
            response.raise_for_status()
            response_json = response.json()
            data = TencentSearchResult.model_validate(response_json)

            if data.data and data.data.normal_list:
                for item in data.data.normal_list.item_list:
                    # 检查 video_info 是否存在
                    if not item.video_info:
                        self.logger.debug(f"Tencent: 过滤掉一个条目，因为它缺少 'videoInfo'。")
                        continue

                    # 过滤掉没有年份的结果
                    if not item.video_info.year or item.video_info.year == 0:
                        self.logger.debug(f"Tencent: 过滤掉结果 '{item.video_info.title}'，因为它缺少有效的年份信息。")
                        continue

                    video_info = item.video_info
                    # 清理标题中的HTML高亮标签
                    unescaped_title = html.unescape(video_info.title)
                    cleaned_title = re.sub(r'<[^>]+>', '', unescaped_title)

                    # 相似度检查：确保搜索词与结果标题相关
                    if keyword.lower() not in cleaned_title.lower():
                        self.logger.debug(f"Tencent: 过滤掉结果 '{cleaned_title}'，因为它与搜索词 '{keyword}' 不直接相关。")
                        continue

                    # 将腾讯的类型映射到我们内部的类型
                    media_type = "movie" if "电影" in video_info.type_name else "tv_series"

                    # 提取总集数
                    episode_count = None
                    if video_info.subject_doc:
                        episode_count = video_info.subject_doc.video_num

                    # 如果搜索时指定了集数，则将其附加到结果中
                    current_episode = episode_info.get("episode") if episode_info else None

                    # 统一冒号
                    final_title = cleaned_title.replace(":", "：")

                    provider_search_info = ProviderSearchInfo(
                        provider=self.provider_name,
                        mediaId=item.doc.id,
                        title=final_title,
                        type=media_type,
                        season=self._get_season_from_title(final_title),
                        year=video_info.year,
                        imageUrl=video_info.img_url,
                        episode_count=episode_count,
                        url=""
                    )
                    results.append(provider_search_info)

        except Exception as e:
            self.logger.error(f"腾讯搜索出错: {e}")

        self.logger.info(f"Tencent: 搜索 '{keyword}' 完成，找到 {len(results)} 个有效结果。")
        return results

    async def get_episodes(self, media_id: str, target_episode_index: Optional[int] = None,
                          db_media_type: Optional[str] = None) -> List[ProviderEpisodeInfo]:
        """获取腾讯视频分集列表"""
        self.logger.info(f"Tencent: 正在获取 {media_id} 的分集列表...")

        try:
            # 使用腾讯的分集API
            url = "https://pbaccess.video.qq.com/trpc.universal_backend_service.page_server_rpc.PageServer/GetPageData"
            params = {
                "video_appid": "3000010",
                "vplatform": "2"
            }

            page_context = f"cid={media_id}&detail_page_type=1&id_type=1&is_skp_style=false&lid=&mvl_strategy_id=&order=&req_from=web_vsite&req_from_second_type=detail_operation&req_type=0&should_apply_tab_in_player=false&should_apply_tab_in_sub_page=false&show_all_episode=false&tab_data_key=lid%3D%26cid%3D{media_id}&un_strategy_id=ea35cb94195c48c091172a047da3e761"

            payload = {
                "page_params": {
                    "cid": media_id,
                    "page_type": "detail_operation",
                    "page_id": "vsite_episode_list_search",
                    "id_type": "1",
                    "page_size": "30",
                    "lid": "",
                    "req_from": "web_vsite",
                    "page_context": page_context,
                    "page_num": "0"
                }
            }

            response = await self._request_with_rate_limit("POST", url, params=params, json=payload)
            response.raise_for_status()
            data = response.json()

            episodes = []
            module_list_datas = data.get("data", {}).get("module_list_datas", [])

            episode_index = 1
            for module_data in module_list_datas:
                for module in module_data.get("module_datas", []):
                    items = module.get("item_data_lists", {}).get("item_datas", [])
                    for item in items:
                        params_data = item.get("item_params", {})
                        if not params_data.get("vid"):
                            continue

                        # 过滤非正片内容
                        title = params_data.get("title", "")
                        if self._EPISODE_BLACKLIST_PATTERN.search(title):
                            self.logger.debug(f"Tencent: 过滤掉非正片内容: '{title}'")
                            continue

                        episode_info = ProviderEpisodeInfo(
                            episode_id=params_data.get("vid", ""),
                            title=title or f"第{episode_index}集",
                            episodeIndex=episode_index,
                            url=""
                        )
                        episodes.append(episode_info)
                        episode_index += 1

                        if target_episode_index and episode_index > target_episode_index:
                            break

            self.logger.info(f"Tencent: 获取到 {len(episodes)} 个分集")
            return episodes

        except Exception as e:
            self.logger.error(f"获取腾讯分集列表出错: {e}")
            return []

    async def get_comments(self, episode_id: str, progress_callback: Optional[Callable] = None) -> List[Dict[str, Any]]:
        """获取腾讯弹幕"""
        self.logger.info(f"Tencent: 正在获取弹幕 vid={episode_id}...")

        try:
            # 腾讯弹幕API
            url = "https://dm.video.qq.com/barrage/base/get_barrage"
            params = {
                "vid": episode_id,
                "otype": "json"
            }

            response = await self._request_with_rate_limit("GET", url, params=params)
            response.raise_for_status()
            data = response.json()

            comments = []
            if data.get("result") == 0 and data.get("data"):
                comment_list = data.get("data", {}).get("barrage_list", [])

                for i, comment in enumerate(comment_list):
                    try:
                        # 腾讯弹幕格式转换
                        time_offset = float(comment.get("time_offset", 0)) / 1000.0  # 毫秒转秒
                        content = comment.get("content", "")

                        formatted_comment = {
                            'cid': i + 1,
                            'p': f"{time_offset:.3f},1,25,16777215,{int(time_offset)},0,0,{i+1}",
                            'm': content,
                            't': round(time_offset, 2)
                        }
                        comments.append(formatted_comment)
                    except Exception as e:
                        self.logger.warning(f"解析腾讯弹幕时出错: {e}")
                        continue

            self.logger.info(f"Tencent: 获取到 {len(comments)} 条弹幕")
            return comments

        except Exception as e:
            self.logger.error(f"获取腾讯弹幕出错: {e}")
            return []
