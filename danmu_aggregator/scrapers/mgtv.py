import re
import json
import logging
from typing import List, Optional, Dict, Any, Callable
from .base import BaseScraper, ProviderSearchInfo, ProviderEpisodeInfo


class MgtvScraper(BaseScraper):
    """芒果TV爬虫"""

    @property
    def provider_name(self) -> str:
        return "mgtv"

    def __init__(self):
        super().__init__()
        # 过滤非正片内容的正则表达式
        self._EPISODE_BLACKLIST_PATTERN = re.compile(
            r"预告|彩蛋|专访|直拍|直播回顾|加更|走心|解忧|纯享|节点|解读|揭秘|赏析|速看|资讯|访谈|番外|短片|纪录片|"
            r"花絮|看点|预告片|精彩|NG|特辑|菜单|片花|首映礼|宣传片|未删减|剪辑版|MV|主题曲|片尾曲|OST|纯享版|会员版|独家版|未播|抢先看|精选合集",
            re.IGNORECASE
        )

    async def search(self, keyword: str, episode_info: Optional[Dict[str, Any]] = None) -> List[ProviderSearchInfo]:
        """搜索芒果TV"""
        self.logger.info(f"MGTV: 正在搜索 '{keyword}'...")

        try:
            # 芒果TV搜索API
            search_url = "https://mobileso.bz.mgtv.com/mobile/recommend/v2"
            params = {
                "collection": "1",
                "text": keyword,
                "idx": 1,
                "size": 20
            }

            response = await self._request_with_rate_limit("GET", search_url, params=params)
            response.raise_for_status()
            data = response.json()

            results = []
            if data.get("code") == 200 and data.get("data", {}).get("contents"):
                for item in data["data"]["contents"]:
                    try:
                        if item.get("type") != "1":  # 只要视频类型
                            continue

                        # 提取媒体ID
                        clip_id = item.get("clipId")
                        if not clip_id:
                            continue

                        # 判断类型
                        media_type = "movie" if item.get("contentType") == "1" else "tv_series"

                        # 提取年份
                        year = None
                        year_str = item.get("year", "")
                        if year_str:
                            try:
                                year = int(year_str)
                            except ValueError:
                                pass

                        result = ProviderSearchInfo(
                            provider=self.provider_name,
                            mediaId=str(clip_id),
                            title=item.get("title", ""),
                            type=media_type,
                            season=1,
                            year=year,
                            episode_count=item.get("episodeCount", 0),
                            imageUrl=item.get("img", ""),
                            url=item.get("url", "")
                        )
                        results.append(result)

                    except Exception as e:
                        self.logger.warning(f"解析芒果TV搜索结果时出错: {e}")
                        continue

            self.logger.info(f"MGTV: 搜索完成，找到 {len(results)} 个结果")
            return results

        except Exception as e:
            self.logger.error(f"芒果TV搜索出错: {e}")
            return []

    async def get_episodes(self, media_id: str, target_episode_index: Optional[int] = None,
                          db_media_type: Optional[str] = None) -> List[ProviderEpisodeInfo]:
        """获取芒果TV分集列表"""
        self.logger.info(f"MGTV: 正在获取 {media_id} 的分集列表...")

        try:
            # 芒果TV分集API
            episodes_url = "https://pcweb.api.mgtv.com/episode/list"
            params = {
                "collection_id": media_id,
                "page": 1,
                "size": 50
            }

            response = await self._request_with_rate_limit("GET", episodes_url, params=params)
            response.raise_for_status()
            data = response.json()

            episodes = []
            if data.get("code") == 200 and data.get("data", {}).get("list"):
                for i, ep in enumerate(data["data"]["list"], 1):
                    try:
                        title = ep.get("t1", f"第{i}集")

                        # 过滤非正片内容
                        if self._EPISODE_BLACKLIST_PATTERN.search(title):
                            self.logger.debug(f"MGTV: 过滤掉非正片内容: '{title}'")
                            continue

                        episode_info = ProviderEpisodeInfo(
                            episode_id=ep.get("clip_id", ""),
                            title=title,
                            episodeIndex=i,
                            url=ep.get("url", "")
                        )
                        episodes.append(episode_info)

                        if target_episode_index and i >= target_episode_index:
                            break

                    except Exception as e:
                        self.logger.warning(f"解析芒果TV分集信息时出错: {e}")
                        continue

            self.logger.info(f"MGTV: 获取到 {len(episodes)} 个分集")
            return episodes

        except Exception as e:
            self.logger.error(f"获取芒果TV分集列表出错: {e}")
            return []

    async def get_comments(self, episode_id: str, progress_callback: Optional[Callable] = None) -> List[Dict[str, Any]]:
        """获取芒果TV弹幕"""
        self.logger.info(f"MGTV: 正在获取弹幕 {episode_id}...")

        try:
            # 芒果TV弹幕API
            danmaku_url = "https://galaxy.bz.mgtv.com/rdbarrage"
            params = {
                "vid": episode_id,
                "cid": episode_id
            }

            response = await self._request_with_rate_limit("GET", danmaku_url, params=params)
            response.raise_for_status()
            data = response.json()

            comments = []
            if data.get("code") == 200 and data.get("data"):
                comment_list = data.get("data", [])

                for i, comment in enumerate(comment_list):
                    try:
                        # 芒果TV弹幕格式转换
                        time_offset = float(comment.get("time", 0))
                        content = comment.get("content", "")

                        formatted_comment = {
                            'cid': i + 1,
                            'p': f"{time_offset:.3f},1,25,16777215,{int(time_offset)},0,0,{i+1}",
                            'm': content,
                            't': round(time_offset, 2)
                        }
                        comments.append(formatted_comment)
                    except Exception as e:
                        self.logger.warning(f"解析芒果TV弹幕时出错: {e}")
                        continue

            self.logger.info(f"MGTV: 获取到 {len(comments)} 条弹幕")
            return comments

        except Exception as e:
            self.logger.error(f"获取芒果TV弹幕出错: {e}")
            return []
