import re
import json
import logging
from typing import List, Optional, Dict, Any, Callable
from .base import BaseScraper, ProviderSearchInfo, ProviderEpisodeInfo


class YoukuScraper(BaseScraper):
    """优酷爬虫"""

    @property
    def provider_name(self) -> str:
        return "youku"

    def __init__(self):
        super().__init__()
        # 过滤非正片内容的正则表达式
        self._EPISODE_BLACKLIST_PATTERN = re.compile(
            r"预告|彩蛋|专访|直拍|直播回顾|加更|走心|解忧|纯享|节点|解读|揭秘|赏析|速看|资讯|访谈|番外|短片|纪录片|"
            r"花絮|看点|预告片|精彩|NG|特辑|菜单|片花|首映礼|宣传片|未删减|剪辑版|MV|主题曲|片尾曲|OST|纯享版|会员版|独家版|未播|抢先看|精选合集",
            re.IGNORECASE
        )

    async def search(self, keyword: str, episode_info: Optional[Dict[str, Any]] = None) -> List[ProviderSearchInfo]:
        """搜索优酷"""
        self.logger.info(f"Youku: 正在搜索 '{keyword}'...")

        try:
            # 优酷搜索API
            search_url = "https://search.youku.com/api/search"
            params = {
                "keyword": keyword,
                "type": "video",
                "orderby": "score",
                "limitstart": 0,
                "limitcount": 20
            }

            response = await self._request_with_rate_limit("GET", search_url, params=params)
            response.raise_for_status()
            data = response.json()

            results = []
            if data.get("pageComponentList"):
                for component in data["pageComponentList"]:
                    if component.get("commonData", {}).get("titleDTO", {}).get("displayName") == "视频":
                        for item in component.get("data", []):
                            try:
                                # 提取媒体ID
                                show_id = item.get("showId")
                                if not show_id:
                                    continue

                                # 判断类型
                                media_type = "movie" if item.get("showType") == "电影" else "tv_series"

                                # 提取年份
                                year = None
                                year_str = item.get("releaseYear", "")
                                if year_str:
                                    try:
                                        year = int(year_str)
                                    except ValueError:
                                        pass

                                result = ProviderSearchInfo(
                                    provider=self.provider_name,
                                    mediaId=str(show_id),
                                    title=item.get("title", ""),
                                    type=media_type,
                                    season=1,
                                    year=year,
                                    episode_count=item.get("totalVideoCount", 0),
                                    imageUrl=item.get("img", ""),
                                    url=item.get("url", "")
                                )
                                results.append(result)

                            except Exception as e:
                                self.logger.warning(f"解析优酷搜索结果时出错: {e}")
                                continue

            self.logger.info(f"Youku: 搜索完成，找到 {len(results)} 个结果")
            return results

        except Exception as e:
            self.logger.error(f"优酷搜索出错: {e}")
            return []

    async def get_episodes(self, media_id: str, target_episode_index: Optional[int] = None,
                          db_media_type: Optional[str] = None) -> List[ProviderEpisodeInfo]:
        """获取优酷分集列表"""
        self.logger.info(f"Youku: 正在获取 {media_id} 的分集列表...")

        try:
            # 优酷分集API
            episodes_url = f"https://list.youku.com/show/episode"
            params = {
                "id": media_id,
                "stage": "reload_"
            }

            response = await self._request_with_rate_limit("GET", episodes_url, params=params)
            response.raise_for_status()
            data = response.json()

            episodes = []
            if data.get("data", {}).get("episode"):
                for i, ep in enumerate(data["data"]["episode"], 1):
                    try:
                        title = ep.get("title", f"第{i}集")

                        # 过滤非正片内容
                        if self._EPISODE_BLACKLIST_PATTERN.search(title):
                            self.logger.debug(f"Youku: 过滤掉非正片内容: '{title}'")
                            continue

                        episode_info = ProviderEpisodeInfo(
                            episode_id=ep.get("videoId", ""),
                            title=title,
                            episodeIndex=i,
                            url=ep.get("url", "")
                        )
                        episodes.append(episode_info)

                        if target_episode_index and i >= target_episode_index:
                            break

                    except Exception as e:
                        self.logger.warning(f"解析优酷分集信息时出错: {e}")
                        continue

            self.logger.info(f"Youku: 获取到 {len(episodes)} 个分集")
            return episodes

        except Exception as e:
            self.logger.error(f"获取优酷分集列表出错: {e}")
            return []

    async def get_comments(self, episode_id: str, progress_callback: Optional[Callable] = None) -> List[Dict[str, Any]]:
        """获取优酷弹幕"""
        self.logger.info(f"Youku: 正在获取弹幕 {episode_id}...")

        try:
            # 优酷弹幕API
            danmaku_url = "https://service.danmu.youku.com/list"
            params = {
                "vid": episode_id,
                "ctime": 0,
                "cid": episode_id
            }

            response = await self._request_with_rate_limit("GET", danmaku_url, params=params)
            response.raise_for_status()
            data = response.json()

            comments = []
            if data.get("result") and data.get("data"):
                comment_list = data.get("data", [])

                for i, comment in enumerate(comment_list):
                    try:
                        # 优酷弹幕格式转换
                        time_offset = float(comment.get("playat", 0))
                        content = comment.get("content", "")

                        formatted_comment = {
                            'cid': i + 1,
                            'p': f"{time_offset:.3f},1,25,16777215,{int(time_offset)},0,0,{i+1}",
                            'm': content,
                            't': round(time_offset, 2)
                        }
                        comments.append(formatted_comment)
                    except Exception as e:
                        self.logger.warning(f"解析优酷弹幕时出错: {e}")
                        continue

            self.logger.info(f"Youku: 获取到 {len(comments)} 条弹幕")
            return comments

        except Exception as e:
            self.logger.error(f"获取优酷弹幕出错: {e}")
            return []
