import json
import re
from typing import List, Optional, Dict, Any, Callable
from .base import BaseScraper, ProviderSearchInfo, ProviderEpisodeInfo
import logging

logger = logging.getLogger(__name__)


class BilibiliScraper(BaseScraper):
    """B站爬虫"""
    
    @property
    def provider_name(self) -> str:
        return "bilibili"
    
    async def search(self, keyword: str, episode_info: Optional[Dict[str, Any]] = None) -> List[ProviderSearchInfo]:
        """搜索B站番剧"""
        self.logger.info(f"Bilibili: 正在搜索 '{keyword}'...")

        try:
            # B站搜索API - 使用更通用的搜索接口
            search_url = "https://api.bilibili.com/x/web-interface/search/all/v2"
            params = {
                "keyword": keyword,
                "page": 1,
                "page_size": 20,
                "search_type": "media_bangumi"
            }

            # 添加必要的请求头
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Referer": "https://www.bilibili.com/",
                "Accept": "application/json, text/plain, */*",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                "Accept-Encoding": "gzip, deflate, br"
            }

            response = await self._request_with_rate_limit("GET", search_url, params=params, headers=headers)
            response.raise_for_status()
            data = response.json()
            
            if data.get("code") != 0:
                self.logger.warning(f"Bilibili搜索失败: {data.get('message', 'Unknown error')}")
                return []
            
            results = []
            search_results = data.get("data", {}).get("result", [])
            
            for item in search_results:
                try:
                    # 提取媒体ID (season_id)
                    media_id = str(item.get("season_id", ""))
                    if not media_id:
                        continue
                    
                    # 构造结果
                    result = ProviderSearchInfo(
                        provider=self.provider_name,
                        mediaId=f"ss{media_id}",  # B站番剧ID格式
                        title=item.get("title", "").replace("<em class=\"keyword\">", "").replace("</em>", ""),
                        url=item.get("url", ""),
                        year=self._extract_year(item.get("pubtime", 0)),
                        episode_count=item.get("ep_size", 0),
                        imageUrl=item.get("cover", ""),
                        type="tv_series",
                        season=1
                    )
                    results.append(result)
                    
                except Exception as e:
                    self.logger.warning(f"解析B站搜索结果时出错: {e}")
                    continue
            
            self.logger.info(f"Bilibili: 搜索完成，找到 {len(results)} 个结果")
            return results
            
        except Exception as e:
            self.logger.error(f"Bilibili搜索出错: {e}")
            return []
    
    def _extract_year(self, timestamp: int) -> Optional[int]:
        """从时间戳提取年份"""
        try:
            if timestamp > 0:
                import datetime
                return datetime.datetime.fromtimestamp(timestamp).year
        except:
            pass
        return None
    
    async def get_episodes(self, media_id: str, target_episode_index: Optional[int] = None, 
                          db_media_type: Optional[str] = None) -> List[ProviderEpisodeInfo]:
        """获取B站番剧分集列表"""
        self.logger.info(f"Bilibili: 正在获取 {media_id} 的分集列表...")
        
        try:
            # 提取season_id
            if media_id.startswith("ss"):
                season_id = media_id[2:]
            else:
                season_id = media_id
            
            # B站番剧详情API
            detail_url = "https://api.bilibili.com/pgc/view/web/season"
            params = {"season_id": season_id}
            
            response = await self._request_with_rate_limit("GET", detail_url, params=params)
            response.raise_for_status()
            data = response.json()
            
            if data.get("code") != 0:
                self.logger.warning(f"获取B站分集失败: {data.get('message', 'Unknown error')}")
                return []
            
            episodes = []
            episode_list = data.get("result", {}).get("episodes", [])
            
            for i, ep in enumerate(episode_list, 1):
                try:
                    episode_info = ProviderEpisodeInfo(
                        episode_id=str(ep.get("cid", "")),  # 使用cid作为episode_id
                        title=ep.get("long_title") or ep.get("title", f"第{i}集"),
                        episodeIndex=i,
                        url=ep.get("link", "")
                    )
                    episodes.append(episode_info)
                    
                    # 如果指定了目标集数，只返回到该集为止
                    if target_episode_index and i >= target_episode_index:
                        break
                        
                except Exception as e:
                    self.logger.warning(f"解析B站分集信息时出错: {e}")
                    continue
            
            self.logger.info(f"Bilibili: 获取到 {len(episodes)} 个分集")
            return episodes
            
        except Exception as e:
            self.logger.error(f"获取B站分集列表出错: {e}")
            return []
    
    async def get_comments(self, episode_id: str, progress_callback: Optional[Callable] = None) -> List[Dict[str, Any]]:
        """获取B站弹幕"""
        self.logger.info(f"Bilibili: 正在获取弹幕 cid={episode_id}...")
        
        try:
            # B站弹幕API
            danmaku_url = f"https://api.bilibili.com/x/v1/dm/list.so?oid={episode_id}"
            
            response = await self._request_with_rate_limit("GET", danmaku_url)
            response.raise_for_status()
            
            # B站弹幕是XML格式
            xml_content = response.text
            
            # 解析XML弹幕
            comments = self._parse_bilibili_xml(xml_content)
            
            self.logger.info(f"Bilibili: 获取到 {len(comments)} 条弹幕")
            return comments
            
        except Exception as e:
            self.logger.error(f"获取B站弹幕出错: {e}")
            return []
    
    def _parse_bilibili_xml(self, xml_content: str) -> List[Dict[str, Any]]:
        """解析B站XML弹幕"""
        comments = []
        
        try:
            # 使用正则表达式解析XML
            pattern = r'<d p="([^"]+)">([^<]+)</d>'
            matches = re.findall(pattern, xml_content)
            
            for i, (p_attr, content) in enumerate(matches):
                try:
                    # p属性格式: "时间,模式,字号,颜色,时间戳,池,用户ID,弹幕ID"
                    p_parts = p_attr.split(',')
                    if len(p_parts) >= 8:
                        comment = {
                            'cid': i + 1,  # 使用序号作为cid
                            'p': p_attr,   # 原始p属性
                            'm': content,  # 弹幕内容
                            't': int(float(p_parts[4])) if p_parts[4].isdigit() else 0  # 时间戳
                        }
                        comments.append(comment)
                except Exception as e:
                    self.logger.warning(f"解析弹幕时出错: {e}")
                    continue
                    
        except Exception as e:
            self.logger.error(f"解析B站XML弹幕出错: {e}")
        
        return comments
