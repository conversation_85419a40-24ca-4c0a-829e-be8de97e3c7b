import json
import re
import time
import hashlib
import html
from urllib.parse import urlencode
from datetime import datetime
from typing import List, Optional, Dict, Any, Callable
from .base import BaseScraper, ProviderSearchInfo, ProviderEpisodeInfo
import logging

logger = logging.getLogger(__name__)


class BilibiliScraper(BaseScraper):
    """B站爬虫 - 直接复制主项目的实现"""

    # WBI签名相关常量
    _WBI_MIXIN_KEY_CACHE: Dict[str, Any] = {"key": None, "timestamp": 0}
    _WBI_MIXIN_KEY_CACHE_TTL = 3600  # Cache for 1 hour
    _WBI_MIXIN_KEY_TABLE = [
        46, 47, 18, 2, 53, 8, 23, 32, 15, 50, 10, 31, 58, 3, 45, 35, 27, 43, 5, 49,
        33, 9, 42, 19, 29, 28, 14, 39, 12, 38, 41, 13, 37, 48, 7, 16, 24, 55, 40,
        61, 26, 17, 0, 1, 60, 51, 30, 4, 22, 25, 54, 21, 56, 59, 6, 63, 57, 62, 11,
        36, 20, 34, 44, 52
    ]

    # 垃圾标题过滤
    _ENG_JUNK = r'NC|OP|ED|SP|OVA|OAD|CM|PV|MV|BDMenu|Menu|Bonus|Recap|Teaser|Trailer|Preview|CD|Disc|Scan|Sample|Logo|Info|EDPV|SongSpot|BDSpot'
    _CN_JUNK = r'特典|预告|广告|菜单|花絮|特辑|速看|资讯|彩蛋|直拍|直播回顾|片头|片尾|幕后|映像|番外篇'
    _JUNK_TITLE_PATTERN = re.compile(
        r'(\[|\【|\b)(' + _ENG_JUNK + r')(\d{1,2})?(\s|_ALL)?(\]|\】|\b)|(' + _CN_JUNK + r')',
        re.IGNORECASE
    )

    def __init__(self):
        super().__init__()
        self._min_interval = 0.5  # B站需要更长的间隔

    @property
    def provider_name(self) -> str:
        return "bilibili"

    async def _create_client(self, **kwargs):
        """创建带有B站专用headers的客户端"""
        client_kwargs = {
            "timeout": 20.0,
            "follow_redirects": True,
            "headers": {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Referer": "https://www.bilibili.com/",
            },
            **kwargs
        }
        return await super()._create_client(**client_kwargs)
    
    async def _get_wbi_mixin_key(self) -> str:
        """获取WBI签名所需的mixin key"""
        now = time.time()
        if (self._WBI_MIXIN_KEY_CACHE["key"] and
            now - self._WBI_MIXIN_KEY_CACHE["timestamp"] < self._WBI_MIXIN_KEY_CACHE_TTL):
            return self._WBI_MIXIN_KEY_CACHE["key"]

        self.logger.info("Bilibili: WBI mixin key expired or not found, fetching new one...")

        try:
            nav_resp = await self._request_with_rate_limit("GET", "https://api.bilibili.com/x/web-interface/nav")
            nav_resp.raise_for_status()
            nav_data = nav_resp.json().get("data", {})
        except Exception as e:
            self.logger.error(f"Bilibili: 获取WBI密钥失败: {e}")
            return "dba4a5925b345b4598b7452c75070bca"  # Fallback

        try:
            img_url = nav_data.get("wbi_img", {}).get("img_url", "")
            sub_url = nav_data.get("wbi_img", {}).get("sub_url", "")

            img_key = img_url.split('/')[-1].split('.')[0]
            sub_key = sub_url.split('/')[-1].split('.')[0]

            mixin_key = "".join([(img_key + sub_key)[i] for i in self._WBI_MIXIN_KEY_TABLE])[:32]

            self._WBI_MIXIN_KEY_CACHE["key"] = mixin_key
            self._WBI_MIXIN_KEY_CACHE["timestamp"] = now
            self.logger.info("Bilibili: Successfully fetched new WBI mixin key.")
            return mixin_key
        except Exception as e:
            self.logger.error(f"Bilibili: Failed to get WBI mixin key: {e}")
            return "dba4a5925b345b4598b7452c75070bca"

    def _get_wbi_signed_params(self, params: Dict[str, Any], mixin_key: str) -> Dict[str, Any]:
        """对参数进行WBI签名"""
        params['wts'] = int(time.time())
        sorted_params = sorted(params.items())
        query = urlencode(sorted_params, safe="!()*'")
        signed_query = query + mixin_key
        w_rid = hashlib.md5(signed_query.encode('utf-8')).hexdigest()
        params['w_rid'] = w_rid
        return params

    async def search(self, keyword: str, episode_info: Optional[Dict[str, Any]] = None) -> List[ProviderSearchInfo]:
        """搜索B站番剧"""
        self.logger.info(f"Bilibili: 正在搜索 '{keyword}'...")

        search_types = ["media_bangumi", "media_ft"]
        all_results = []

        for search_type in search_types:
            try:
                results = await self._search_by_type(keyword, search_type, episode_info)
                all_results.extend(results)
            except Exception as e:
                self.logger.error(f"Bilibili: 搜索类型 '{search_type}' 失败: {e}")
                continue

        # 去重
        final_results = list({item.mediaId: item for item in all_results}.values())

        self.logger.info(f"Bilibili: 搜索完成，找到 {len(final_results)} 个结果")
        return final_results
    
    async def _search_by_type(self, keyword: str, search_type: str, episode_info: Optional[Dict[str, Any]] = None) -> List[ProviderSearchInfo]:
        """按类型搜索B站内容"""
        self.logger.debug(f"Bilibili: Searching for type '{search_type}' with keyword '{keyword}'")

        search_params = {"keyword": keyword, "search_type": search_type}
        base_url = "https://api.bilibili.com/x/web-interface/wbi/search/type"
        mixin_key = await self._get_wbi_mixin_key()
        signed_params = self._get_wbi_signed_params(search_params, mixin_key)
        url = f"{base_url}?{urlencode(signed_params)}"

        results = []
        try:
            response = await self._request_with_rate_limit("GET", url)
            response.raise_for_status()
            data = response.json()

            if data.get("code") == 0 and data.get("data", {}).get("result"):
                self.logger.info(f"Bilibili: API call for type '{search_type}' successful, found {len(data['data']['result'])} items.")
                for item in data["data"]["result"]:
                    if self._JUNK_TITLE_PATTERN.search(item.get("title", "")):
                        self.logger.debug(f"Bilibili: Filtering out junk title: '{item.get('title', '')}'")
                        continue

                    media_id = f"ss{item.get('season_id')}" if item.get('season_id') else f"bv{item.get('bvid')}" if item.get('bvid') else ""
                    if not media_id:
                        continue

                    media_type = "movie" if item.get("season_type_name") == "电影" else "tv_series"

                    year = None
                    try:
                        if item.get('pubdate'):
                            if isinstance(item['pubdate'], int):
                                year = datetime.fromtimestamp(item['pubdate']).year
                            elif isinstance(item['pubdate'], str) and len(item['pubdate']) >= 4:
                                year = int(item['pubdate'][:4])
                        elif item.get('pubtime'):
                            year = datetime.fromtimestamp(item['pubtime']).year
                    except (ValueError, TypeError, OSError):
                        pass

                    unescaped_title = html.unescape(item.get("title", ""))
                    cleaned_title = re.sub(r'<[^>]+>', '', unescaped_title).replace(":", "：")

                    results.append(ProviderSearchInfo(
                        provider=self.provider_name,
                        mediaId=media_id,
                        title=cleaned_title,
                        type=media_type,
                        season=self._get_season_from_title(cleaned_title),
                        year=year,
                        episode_count=item.get("ep_size", 0),
                        imageUrl=item.get("cover", ""),
                        url=item.get("url", "")
                    ))
            else:
                self.logger.warning(f"Bilibili: API call for type '{search_type}' failed or returned no results.")

        except Exception as e:
            self.logger.error(f"Bilibili: 搜索类型 '{search_type}' 时出错: {e}")

        return results

    def _get_season_from_title(self, title: str) -> int:
        """从标题中提取季数"""
        # 简化的季数提取逻辑
        season_patterns = [
            (re.compile(r'第([一二三四五六七八九十]+)季'), lambda m: self._chinese_to_number(m.group(1))),
            (re.compile(r'Season\s*(\d+)', re.IGNORECASE), lambda m: int(m.group(1))),
            (re.compile(r'S(\d+)', re.IGNORECASE), lambda m: int(m.group(1))),
            (re.compile(r'(\d+)$'), lambda m: int(m.group(1)))
        ]

        for pattern, handler in season_patterns:
            match = pattern.search(title)
            if match:
                try:
                    season = handler(match)
                    if season and 1 <= season <= 20:
                        return season
                except (ValueError, KeyError, IndexError):
                    continue
        return 1

    def _chinese_to_number(self, chinese_num: str) -> int:
        """将中文数字转换为阿拉伯数字"""
        chinese_map = {
            '一': 1, '二': 2, '三': 3, '四': 4, '五': 5,
            '六': 6, '七': 7, '八': 8, '九': 9, '十': 10
        }
        return chinese_map.get(chinese_num, 1)

    async def get_episodes(self, media_id: str, target_episode_index: Optional[int] = None,
                          db_media_type: Optional[str] = None) -> List[ProviderEpisodeInfo]:
        """获取B站分集列表"""
        if media_id.startswith("ss"):
            return await self._get_pgc_episodes(media_id, target_episode_index)
        elif media_id.startswith("bv"):
            return await self._get_ugc_episodes(media_id, target_episode_index)
        return []

    async def _get_pgc_episodes(self, media_id: str, target_episode_index: Optional[int] = None) -> List[ProviderEpisodeInfo]:
        """获取PGC（番剧）分集列表"""
        season_id = media_id[2:]  # 去掉 "ss" 前缀
        url = f"https://api.bilibili.com/pgc/view/web/ep/list?season_id={season_id}"

        try:
            response = await self._request_with_rate_limit("GET", url)
            response.raise_for_status()
            data = response.json()

            if data.get("code") == 0 and data.get("result", {}).get("episodes"):
                episodes = []
                for i, ep in enumerate(data["result"]["episodes"], 1):
                    # 过滤垃圾标题
                    title_to_check = ep.get("show_title") or ep.get("long_title", "")
                    if self._JUNK_TITLE_PATTERN.search(title_to_check):
                        self.logger.debug(f"Bilibili: 过滤掉PGC分集: '{title_to_check}'")
                        continue

                    episode_info = ProviderEpisodeInfo(
                        episode_id=f"{ep.get('aid', '')},{ep.get('cid', '')}",
                        title=ep.get("long_title") or ep.get("title", f"第{i}集"),
                        episodeIndex=i,
                        url=f"https://www.bilibili.com/bangumi/play/ep{ep.get('id', '')}"
                    )
                    episodes.append(episode_info)

                    if target_episode_index and i >= target_episode_index:
                        break

                return episodes

        except Exception as e:
            self.logger.error(f"Bilibili: 获取PGC分集列表失败 (media_id={media_id}): {e}")

        return []

    async def _get_ugc_episodes(self, media_id: str, target_episode_index: Optional[int] = None) -> List[ProviderEpisodeInfo]:
        """获取UGC（用户投稿）分集列表"""
        bvid = media_id[2:]  # 去掉 "bv" 前缀
        url = f"https://api.bilibili.com/x/web-interface/view?bvid={bvid}"

        try:
            response = await self._request_with_rate_limit("GET", url)
            response.raise_for_status()
            data = response.json()

            if data.get("code") == 0 and data.get("data", {}).get("pages"):
                episodes = []
                aid = data["data"].get("aid", "")

                for i, page in enumerate(data["data"]["pages"], 1):
                    episode_info = ProviderEpisodeInfo(
                        episode_id=f"{aid},{page.get('cid', '')}",
                        title=page.get("part", f"P{i}"),
                        episodeIndex=i,
                        url=f"https://www.bilibili.com/video/{bvid}?p={i}"
                    )
                    episodes.append(episode_info)

                    if target_episode_index and i >= target_episode_index:
                        break

                return episodes

        except Exception as e:
            self.logger.error(f"Bilibili: 获取UGC分集列表失败 (media_id={media_id}): {e}")

        return []

    async def get_comments(self, episode_id: str, progress_callback: Optional[Callable] = None) -> List[Dict[str, Any]]:
        """获取B站弹幕"""
        try:
            aid_str, cid_str = episode_id.split(',')
            aid, cid = int(aid_str), int(cid_str)
        except (ValueError, IndexError):
            self.logger.error(f"Bilibili: 无效的 episode_id 格式: '{episode_id}'")
            return []

        self.logger.info(f"Bilibili: 正在获取弹幕 aid={aid}, cid={cid}...")

        try:
            # B站弹幕API
            danmaku_url = f"https://api.bilibili.com/x/v1/dm/list.so?oid={cid}"

            response = await self._request_with_rate_limit("GET", danmaku_url)
            response.raise_for_status()

            # B站弹幕是XML格式
            xml_content = response.text

            # 解析XML弹幕
            comments = self._parse_bilibili_xml(xml_content)

            self.logger.info(f"Bilibili: 获取到 {len(comments)} 条弹幕")
            return comments

        except Exception as e:
            self.logger.error(f"获取B站弹幕出错: {e}")
            return []

    def _parse_bilibili_xml(self, xml_content: str) -> List[Dict[str, Any]]:
        """解析B站XML弹幕"""
        comments = []

        try:
            # 使用正则表达式解析XML
            pattern = r'<d p="([^"]+)">([^<]+)</d>'
            matches = re.findall(pattern, xml_content)

            for i, (p_attr, content) in enumerate(matches):
                try:
                    # p属性格式: "时间,模式,字号,颜色,时间戳,池,用户ID,弹幕ID"
                    p_parts = p_attr.split(',')
                    if len(p_parts) >= 8:
                        timestamp = float(p_parts[0])
                        comment = {
                            'cid': i + 1,  # 使用序号作为cid
                            'p': f"{timestamp:.3f},{p_parts[1]},{p_parts[3]},[bilibili]",  # 简化的p属性
                            'm': content,  # 弹幕内容
                            't': round(timestamp, 2)  # 时间戳
                        }
                        comments.append(comment)
                except Exception as e:
                    self.logger.warning(f"解析弹幕时出错: {e}")
                    continue

        except Exception as e:
            self.logger.error(f"解析B站XML弹幕出错: {e}")

        return comments
    
    async def get_episodes(self, media_id: str, target_episode_index: Optional[int] = None, 
                          db_media_type: Optional[str] = None) -> List[ProviderEpisodeInfo]:
        """获取B站番剧分集列表"""
        self.logger.info(f"Bilibili: 正在获取 {media_id} 的分集列表...")
        
        try:
            # 提取season_id
            if media_id.startswith("ss"):
                season_id = media_id[2:]
            else:
                season_id = media_id
            
            # B站番剧详情API
            detail_url = "https://api.bilibili.com/pgc/view/web/season"
            params = {"season_id": season_id}
            
            response = await self._request_with_rate_limit("GET", detail_url, params=params)
            response.raise_for_status()
            data = response.json()
            
            if data.get("code") != 0:
                self.logger.warning(f"获取B站分集失败: {data.get('message', 'Unknown error')}")
                return []
            
            episodes = []
            episode_list = data.get("result", {}).get("episodes", [])
            
            for i, ep in enumerate(episode_list, 1):
                try:
                    episode_info = ProviderEpisodeInfo(
                        episode_id=str(ep.get("cid", "")),  # 使用cid作为episode_id
                        title=ep.get("long_title") or ep.get("title", f"第{i}集"),
                        episodeIndex=i,
                        url=ep.get("link", "")
                    )
                    episodes.append(episode_info)
                    
                    # 如果指定了目标集数，只返回到该集为止
                    if target_episode_index and i >= target_episode_index:
                        break
                        
                except Exception as e:
                    self.logger.warning(f"解析B站分集信息时出错: {e}")
                    continue
            
            self.logger.info(f"Bilibili: 获取到 {len(episodes)} 个分集")
            return episodes
            
        except Exception as e:
            self.logger.error(f"获取B站分集列表出错: {e}")
            return []
    
    async def get_comments(self, episode_id: str, progress_callback: Optional[Callable] = None) -> List[Dict[str, Any]]:
        """获取B站弹幕"""
        self.logger.info(f"Bilibili: 正在获取弹幕 cid={episode_id}...")
        
        try:
            # B站弹幕API
            danmaku_url = f"https://api.bilibili.com/x/v1/dm/list.so?oid={episode_id}"
            
            response = await self._request_with_rate_limit("GET", danmaku_url)
            response.raise_for_status()
            
            # B站弹幕是XML格式
            xml_content = response.text
            
            # 解析XML弹幕
            comments = self._parse_bilibili_xml(xml_content)
            
            self.logger.info(f"Bilibili: 获取到 {len(comments)} 条弹幕")
            return comments
            
        except Exception as e:
            self.logger.error(f"获取B站弹幕出错: {e}")
            return []
    
    def _parse_bilibili_xml(self, xml_content: str) -> List[Dict[str, Any]]:
        """解析B站XML弹幕"""
        comments = []
        
        try:
            # 使用正则表达式解析XML
            pattern = r'<d p="([^"]+)">([^<]+)</d>'
            matches = re.findall(pattern, xml_content)
            
            for i, (p_attr, content) in enumerate(matches):
                try:
                    # p属性格式: "时间,模式,字号,颜色,时间戳,池,用户ID,弹幕ID"
                    p_parts = p_attr.split(',')
                    if len(p_parts) >= 8:
                        comment = {
                            'cid': i + 1,  # 使用序号作为cid
                            'p': p_attr,   # 原始p属性
                            'm': content,  # 弹幕内容
                            't': int(float(p_parts[4])) if p_parts[4].isdigit() else 0  # 时间戳
                        }
                        comments.append(comment)
                except Exception as e:
                    self.logger.warning(f"解析弹幕时出错: {e}")
                    continue
                    
        except Exception as e:
            self.logger.error(f"解析B站XML弹幕出错: {e}")
        
        return comments
