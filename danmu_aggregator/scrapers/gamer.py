import re
import json
import logging
from typing import List, Optional, Dict, Any, Callable
from .base import BaseScraper, ProviderSearchInfo, ProviderEpisodeInfo


class GamerScraper(BaseScraper):
    """巴哈姆特动画疯爬虫"""

    @property
    def provider_name(self) -> str:
        return "gamer"

    def __init__(self):
        super().__init__()
        # 过滤非正片内容的正则表达式
        self._EPISODE_BLACKLIST_PATTERN = re.compile(
            r"预告|彩蛋|专访|直拍|直播回顾|加更|走心|解忧|纯享|节点|解读|揭秘|赏析|速看|资讯|访谈|番外|短片|纪录片|"
            r"花絮|看点|预告片|精彩|NG|特辑|菜单|片花|首映礼|宣传片|未删减|剪辑版|MV|主题曲|片尾曲|OST|纯享版|会员版|独家版|未播|抢先看|精选合集",
            re.IGNORECASE
        )

    async def search(self, keyword: str, episode_info: Optional[Dict[str, Any]] = None) -> List[ProviderSearchInfo]:
        """搜索巴哈姆特"""
        self.logger.info(f"Gamer: 正在搜索 '{keyword}'...")

        try:
            # 巴哈姆特搜索API
            search_url = "https://ani.gamer.com.tw/ajax/animeSearch.php"
            params = {
                "kw": keyword,
                "limit": 20
            }

            response = await self._request_with_rate_limit("GET", search_url, params=params)
            response.raise_for_status()
            data = response.json()

            results = []
            if data.get("data"):
                for item in data["data"]:
                    try:
                        # 提取媒体ID
                        anime_sn = item.get("animeSn")
                        if not anime_sn:
                            continue

                        # 提取年份
                        year = None
                        year_str = item.get("year", "")
                        if year_str:
                            try:
                                year = int(year_str)
                            except ValueError:
                                pass

                        result = ProviderSearchInfo(
                            provider=self.provider_name,
                            mediaId=str(anime_sn),
                            title=item.get("title", ""),
                            type="tv_series",  # 巴哈姆特主要是动画
                            season=1,
                            year=year,
                            episode_count=item.get("totalEpisode", 0),
                            imageUrl=item.get("cover", ""),
                            url=f"https://ani.gamer.com.tw/animeVideo.php?sn={anime_sn}"
                        )
                        results.append(result)

                    except Exception as e:
                        self.logger.warning(f"解析巴哈姆特搜索结果时出错: {e}")
                        continue

            self.logger.info(f"Gamer: 搜索完成，找到 {len(results)} 个结果")
            return results

        except Exception as e:
            self.logger.error(f"巴哈姆特搜索出错: {e}")
            return []

    async def get_episodes(self, media_id: str, target_episode_index: Optional[int] = None,
                          db_media_type: Optional[str] = None) -> List[ProviderEpisodeInfo]:
        """获取巴哈姆特分集列表"""
        self.logger.info(f"Gamer: 正在获取 {media_id} 的分集列表...")

        try:
            # 巴哈姆特分集API
            episodes_url = f"https://ani.gamer.com.tw/ajax/videoStart.php"
            params = {
                "sn": media_id
            }

            response = await self._request_with_rate_limit("GET", episodes_url, params=params)
            response.raise_for_status()
            data = response.json()

            episodes = []
            if data.get("data", {}).get("episode"):
                for i, ep in enumerate(data["data"]["episode"], 1):
                    try:
                        title = ep.get("title", f"第{i}集")

                        # 过滤非正片内容
                        if self._EPISODE_BLACKLIST_PATTERN.search(title):
                            self.logger.debug(f"Gamer: 过滤掉非正片内容: '{title}'")
                            continue

                        episode_info = ProviderEpisodeInfo(
                            episode_id=ep.get("sn", ""),
                            title=title,
                            episodeIndex=i,
                            url=f"https://ani.gamer.com.tw/animeVideo.php?sn={ep.get('sn', '')}"
                        )
                        episodes.append(episode_info)

                        if target_episode_index and i >= target_episode_index:
                            break

                    except Exception as e:
                        self.logger.warning(f"解析巴哈姆特分集信息时出错: {e}")
                        continue

            self.logger.info(f"Gamer: 获取到 {len(episodes)} 个分集")
            return episodes

        except Exception as e:
            self.logger.error(f"获取巴哈姆特分集列表出错: {e}")
            return []

    async def get_comments(self, episode_id: str, progress_callback: Optional[Callable] = None) -> List[Dict[str, Any]]:
        """获取巴哈姆特弹幕"""
        self.logger.info(f"Gamer: 正在获取弹幕 {episode_id}...")

        try:
            # 巴哈姆特弹幕API
            danmaku_url = "https://ani.gamer.com.tw/ajax/danmuGet.php"
            params = {
                "sn": episode_id
            }

            response = await self._request_with_rate_limit("GET", danmaku_url, params=params)
            response.raise_for_status()
            data = response.json()

            comments = []
            if data.get("data"):
                comment_list = data.get("data", [])

                for i, comment in enumerate(comment_list):
                    try:
                        # 巴哈姆特弹幕格式转换
                        time_offset = float(comment.get("time", 0))
                        content = comment.get("text", "")
                        color = comment.get("color", "16777215")

                        formatted_comment = {
                            'cid': i + 1,
                            'p': f"{time_offset:.3f},1,25,{color},{int(time_offset)},0,0,{i+1}",
                            'm': content,
                            't': round(time_offset, 2)
                        }
                        comments.append(formatted_comment)
                    except Exception as e:
                        self.logger.warning(f"解析巴哈姆特弹幕时出错: {e}")
                        continue

            self.logger.info(f"Gamer: 获取到 {len(comments)} 条弹幕")
            return comments

        except Exception as e:
            self.logger.error(f"获取巴哈姆特弹幕出错: {e}")
            return []
