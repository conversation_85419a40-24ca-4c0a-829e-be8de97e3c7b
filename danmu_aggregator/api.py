import base64
import json
import hashlib
import re
from typing import Optional, Dict, Any, List
from fastapi import FastAPI, Query, Path, HTTPException
from models import DandanSearchEpisodesResponse, DandanAnimeInfo, DandanEpisodeInfo, CommentResponse, Comment
from scraper_manager import ScraperManager
from utils import parse_search_term, encode_episode_id, decode_episode_id
import logging

logger = logging.getLogger(__name__)

app = FastAPI(title="实时弹幕聚合API", description="兼容dandanplay API的实时弹幕服务", version="1.0.0")

# 全局爬虫管理器
scraper_manager = ScraperManager()

# 类型映射
TYPE_MAPPING = {
    "tv_series": "tvseries",
    "movie": "movie",
    "ova": "ova",
    "web": "web"
}

TYPE_DESC_MAPPING = {
    "tv_series": "TV",
    "movie": "剧场版",
    "ova": "OVA",
    "web": "网络动画"
}


@app.get("/search/episodes", response_model=DandanSearchEpisodesResponse, summary="搜索节目和分集")
async def search_episodes(
    anime: str = Query(..., description="节目名称"),
    episode: Optional[str] = Query(None, description="分集标题 (通常是数字)")
):
    """
    兼容dandanplay的搜索接口，实时从各大视频网站搜索
    """
    try:
        logger.info(f"收到搜索请求: anime='{anime}', episode='{episode}'")
        
        # 解析搜索词
        parsed_info = parse_search_term(anime.strip())
        title_to_search = parsed_info["title"]
        season_to_search = parsed_info.get("season")
        episode_from_title = parsed_info.get("episode")
        
        # 优先使用独立的episode参数
        episode_number_from_param = int(episode) if episode and episode.isdigit() else None
        final_episode_to_search = episode_number_from_param if episode_number_from_param is not None else episode_from_title
        
        # 构造搜索参数
        episode_info = None
        if season_to_search or final_episode_to_search:
            episode_info = {
                "season": season_to_search or 1,
                "episode": final_episode_to_search
            }
        
        # 实时搜索所有源
        search_results = await scraper_manager.search_all(title_to_search, episode_info)
        
        if not search_results:
            logger.info(f"未找到搜索结果: '{title_to_search}'")
            return DandanSearchEpisodesResponse(animes=[])
        
        # 按提供商分组并获取分集信息
        grouped_animes: Dict[str, DandanAnimeInfo] = {}
        
        for result in search_results:
            try:
                # 获取分集列表
                episodes = await scraper_manager.get_episodes(
                    result.provider, 
                    result.media_id, 
                    target_episode_index=final_episode_to_search
                )
                
                if not episodes:
                    continue
                
                # 创建唯一的anime_id (基于provider和media_id的hash)
                anime_id_str = f"{result.provider}_{result.media_id}"
                anime_id = abs(hash(anime_id_str)) % (10**8)  # 生成8位数字ID
                
                # 转换分集信息
                dandan_episodes = []
                for ep in episodes:
                    # 编码episode_id，包含provider和原始episode_id
                    encoded_episode_id = encode_episode_id(result.provider, ep.episode_id)
                    
                    dandan_episodes.append(DandanEpisodeInfo(
                        episodeId=encoded_episode_id,
                        episodeTitle=ep.title
                    ))
                
                if dandan_episodes:
                    # 映射类型
                    dandan_type = TYPE_MAPPING.get(result.type, "tvseries")
                    dandan_type_desc = TYPE_DESC_MAPPING.get(result.type, "TV")
                    
                    grouped_animes[anime_id_str] = DandanAnimeInfo(
                        animeId=anime_id,
                        animeTitle=result.title,
                        imageUrl=result.image_url or "",
                        searchKeyword=anime.strip(),
                        type=dandan_type,
                        typeDescription=dandan_type_desc,
                        episodes=dandan_episodes
                    )
                    
            except Exception as e:
                logger.error(f"处理搜索结果时出错 ({result.provider}): {e}")
                continue
        
        result_animes = list(grouped_animes.values())
        logger.info(f"搜索完成，返回 {len(result_animes)} 个作品，共 {sum(len(a.episodes) for a in result_animes)} 个分集")
        
        return DandanSearchEpisodesResponse(animes=result_animes)
        
    except Exception as e:
        logger.error(f"搜索时发生错误: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")


@app.get("/comment/{episode_id}", response_model=CommentResponse, summary="获取弹幕")
async def get_comments(
    episode_id: int = Path(..., description="分集ID"),
    chConvert: int = Query(0, description="中文简繁转换。0-不转换，1-转换为简体，2-转换为繁体。")
):
    """
    兼容dandanplay的弹幕获取接口，实时从对应网站获取弹幕
    """
    try:
        logger.info(f"收到弹幕请求: episode_id={episode_id}")
        
        # 解码episode_id，获取provider和原始episode_id
        provider, original_episode_id = decode_episode_id(episode_id)
        
        if not provider or not original_episode_id:
            logger.warning(f"无效的episode_id: {episode_id}")
            return CommentResponse(count=0, comments=[])
        
        logger.info(f"解码结果: provider={provider}, original_episode_id={original_episode_id}")
        
        # 实时获取弹幕
        comments_data = await scraper_manager.get_comments(provider, original_episode_id)
        
        if not comments_data:
            logger.info(f"未找到弹幕: provider={provider}, episode_id={original_episode_id}")
            return CommentResponse(count=0, comments=[])
        
        # 转换为dandanplay格式
        dandan_comments = []
        for comment in comments_data:
            try:
                dandan_comment = Comment(
                    cid=comment.get('cid', 0),
                    p=comment.get('p', ''),
                    m=comment.get('m', '')
                )
                dandan_comments.append(dandan_comment)
            except Exception as e:
                logger.warning(f"转换弹幕格式时出错: {e}")
                continue
        
        # 简繁转换处理
        if chConvert in [1, 2]:
            try:
                from opencc import OpenCC
                converter = None
                if chConvert == 1:
                    converter = OpenCC('t2s')  # 繁转简
                elif chConvert == 2:
                    converter = OpenCC('s2t')  # 简转繁
                
                if converter:
                    for comment in dandan_comments:
                        comment.m = converter.convert(comment.m)
            except ImportError:
                logger.warning("OpenCC未安装，跳过简繁转换")
            except Exception as e:
                logger.warning(f"简繁转换时出错: {e}")
        
        logger.info(f"弹幕获取完成: provider={provider}, 弹幕数量={len(dandan_comments)}")
        
        return CommentResponse(count=len(dandan_comments), comments=dandan_comments)
        
    except Exception as e:
        logger.error(f"获取弹幕时发生错误: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取弹幕失败: {str(e)}")


@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时清理资源"""
    await scraper_manager.close_all()


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
