import asyncio
import logging
from typing import Dict, List, Optional, Any
import httpx
from scrapers.base import BaseScraper
from scrapers.bilibili import BilibiliScraper
from scrapers.tencent import TencentScraper
from scrapers.mgtv import MgtvScraper
from scrapers.youku import YoukuScraper
from scrapers.gamer import GamerScraper
from models import SearchResult, EpisodeInfo

logger = logging.getLogger(__name__)


class ScraperManager:
    """管理所有弹幕爬虫的管理器"""
    
    def __init__(self):
        self.scrapers: Dict[str, BaseScraper] = {}
        self._initialize_scrapers()
    
    def _initialize_scrapers(self):
        """初始化所有爬虫"""
        scraper_classes = [
            <PERSON><PERSON><PERSON><PERSON>Scraper,
            TencentScraper,
            MgtvScraper,
            <PERSON><PERSON><PERSON>craper,
            GamerScraper,
        ]
        
        for scraper_class in scraper_classes:
            try:
                scraper = scraper_class()
                self.scrapers[scraper.provider_name] = scraper
                logger.info(f"已初始化爬虫: {scraper.provider_name}")
            except Exception as e:
                logger.error(f"初始化爬虫 {scraper_class.__name__} 失败: {e}")
    
    async def search_all(self, keyword: str, episode_info: Optional[Dict[str, Any]] = None) -> List[SearchResult]:
        """在所有爬虫中搜索"""
        all_results = []
        tasks = []
        
        for provider_name, scraper in self.scrapers.items():
            task = self._search_single(scraper, keyword, episode_info)
            tasks.append(task)
        
        results_list = await asyncio.gather(*tasks, return_exceptions=True)
        
        for i, results in enumerate(results_list):
            if isinstance(results, Exception):
                provider_name = list(self.scrapers.keys())[i]
                logger.error(f"搜索 {provider_name} 时出错: {results}")
            elif isinstance(results, list):
                all_results.extend(results)
        
        return all_results
    
    async def _search_single(self, scraper: BaseScraper, keyword: str, episode_info: Optional[Dict[str, Any]]) -> List[SearchResult]:
        """在单个爬虫中搜索"""
        try:
            results = await scraper.search(keyword, episode_info)
            return [
                SearchResult(
                    title=r.title,
                    url=r.url or "",
                    year=r.year,
                    episode_count=r.episode_count,
                    image_url=r.imageUrl,
                    provider=r.provider,
                    media_id=r.mediaId
                ) for r in results
            ]
        except Exception as e:
            logger.error(f"搜索 {scraper.provider_name} 时出错: {e}")
            return []
    
    async def get_episodes(self, provider: str, media_id: str, target_episode_index: Optional[int] = None) -> List[EpisodeInfo]:
        """获取指定媒体的分集列表"""
        if provider not in self.scrapers:
            raise ValueError(f"未知的爬虫: {provider}")
        
        scraper = self.scrapers[provider]
        try:
            episodes = await scraper.get_episodes(media_id, target_episode_index)
            return [
                EpisodeInfo(
                    episode_id=ep.episode_id,
                    title=ep.title,
                    url=ep.url or "",
                    episode_index=ep.episodeIndex
                ) for ep in episodes
            ]
        except Exception as e:
            logger.error(f"获取 {provider} 分集列表时出错: {e}")
            return []
    
    async def get_comments(self, provider: str, episode_id: str) -> List[Dict[str, Any]]:
        """获取指定分集的弹幕"""
        if provider not in self.scrapers:
            raise ValueError(f"未知的爬虫: {provider}")
        
        scraper = self.scrapers[provider]
        try:
            comments = await scraper.get_comments(episode_id)
            return comments
        except Exception as e:
            logger.error(f"获取 {provider} 弹幕时出错: {e}")
            return []
    
    async def close_all(self):
        """关闭所有爬虫"""
        for scraper in self.scrapers.values():
            try:
                await scraper.close()
            except Exception as e:
                logger.error(f"关闭爬虫时出错: {e}")
