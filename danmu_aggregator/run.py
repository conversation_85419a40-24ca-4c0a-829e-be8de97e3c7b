#!/usr/bin/env python3
"""
实时弹幕聚合器启动脚本
"""

import sys
import os
import logging
import uvicorn

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def main():
    """主函数"""
    logger.info("启动实时弹幕聚合器...")
    
    # 启动FastAPI应用
    uvicorn.run(
        "api:app",  # 使用字符串形式的模块路径
        host="0.0.0.0",
        port=8000,
        log_level="info",
        reload=False
    )


if __name__ == "__main__":
    main()
